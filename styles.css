:root {
  --bg-color: #f8fafc;
  --container-bg: #ffffff;
  --text-color: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --status-container-bg: #f8fafc;
  --toggle-bg: rgba(100, 116, 139, 0.2);
  --toggle-handle: white;
  --bottom-container-bg: #f8fafc;
  --lang-btn-bg: #ffffff;
  --lang-btn-color: #64748b;
  --lang-btn-border: #e2e8f0;
  --lang-btn-active-bg: #dbeafe;
  --lang-btn-active-color: #2563eb;
  --lang-btn-active-border: #2563eb;
  --shadow-color: rgba(15, 23, 42, 0.08);
  --shadow-small: rgba(15, 23, 42, 0.04);
  --shadow-medium: rgba(15, 23, 42, 0.12);
  --performance-bg: #f8fafc;
  --performance-border: #e2e8f0;
  --metric-bg: #ffffff;
  --debug-btn-bg: #f1f5f9;
  --debug-btn-active: #2563eb;
  --accent-primary: #2563eb;
  --accent-success: #059669;
  --accent-warning: #d97706;
  --accent-danger: #dc2626;
}

[data-theme="dark"] {
  --bg-color: #0f172a;
  --container-bg: #1e293b;
  --text-color: #f1f5f9;
  --text-secondary: #94a3b8;
  --border-color: #334155;
  --status-container-bg: #0f172a;
  --toggle-bg: rgba(148, 163, 184, 0.2);
  --toggle-handle: #f1f5f9;
  --bottom-container-bg: #0f172a;
  --lang-btn-bg: #334155;
  --lang-btn-color: #f1f5f9;
  --lang-btn-border: #475569;
  --lang-btn-active-bg: #1e40af;
  --lang-btn-active-color: #f1f5f9;
  --lang-btn-active-border: #3b82f6;
  --shadow-color: rgba(0, 0, 0, 0.4);
  --shadow-small: rgba(0, 0, 0, 0.2);
  --shadow-medium: rgba(0, 0, 0, 0.3);
  --performance-bg: #0f172a;
  --performance-border: #334155;
  --metric-bg: #1e293b;
  --debug-btn-bg: #334155;
  --debug-btn-active: #3b82f6;
  --accent-primary: #3b82f6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-danger: #ef4444;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
  width: 360px;
  padding: 0;
  background-color: var(--container-bg);
  box-shadow: 0 8px 32px var(--shadow-color), 0 2px 8px var(--shadow-small);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: container-appear 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color);
}

@keyframes container-appear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-2px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 28px 24px;
  position: relative;
  background: var(--container-bg);
  margin-bottom: 0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Bottom container and language selector */
.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 28px;
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  border-radius: 0 0 20px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.lang-selector {
  display: flex;
  gap: 5px;
  justify-content: center;
  z-index: 10;
}

.lang-btn {
  width: 44px;
  height: 44px;
  border-radius: 14px;
  border: 2px solid var(--lang-btn-border);
  background-color: var(--lang-btn-bg);
  color: var(--lang-btn-color);
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 3px 6px var(--shadow-small);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.lang-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.lang-btn:hover {
  background-color: var(--lang-btn-bg);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.lang-btn:hover::before {
  width: 100%;
  height: 100%;
}

.lang-btn:active {
  transform: translateY(0) scale(0.98);
}

.lang-btn.lang-active {
  border-color: var(--lang-btn-active-border);
  color: var(--lang-btn-active-color);
  background-color: var(--lang-btn-active-bg);
  box-shadow: 0 4px 12px var(--shadow-medium);
  transform: translateY(-2px) scale(1.05);
}

/* Dark mode toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.theme-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-secondary);
  transition: fill 0.3s ease;
}

.header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--accent-primary) 0%,
    var(--accent-success) 25%,
    var(--accent-warning) 50%,
    var(--accent-danger) 75%,
    var(--accent-primary) 100%
  );
  border-radius: 16px 16px 0 0;
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
}

h1 {
  font-size: 26px;
  color: var(--text-color);
  margin-bottom: 20px;
  text-align: center;
  font-weight: 700;
  letter-spacing: -0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-success));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.toggle-container {
  display: flex;
  align-items: center;
  margin-top: 12px;
  background-color: var(--status-container-bg);
  padding: 12px 20px;
  border-radius: 28px;
  box-shadow: 0 4px 8px var(--shadow-small);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#toggleStatus {
  margin-left: 14px;
  font-size: 15px;
  color: var(--text-color);
  font-weight: 600;
  transition: color 0.3s ease;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 58px;
  height: 30px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-bg);
  transition: .3s;
  box-shadow: inset 0 1px 3px var(--shadow-small);
  overflow: hidden;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: var(--toggle-handle);
  transition: .3s;
  box-shadow: 0 2px 4px var(--shadow-small);
  z-index: 2;
}

.slider:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 33%,
    #34A853 33%,
    #34A853 67%,
    #FBBC05 67%,
    #FBBC05 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

input:checked + .slider {
  background-color: transparent;
}

input:checked + .slider:after {
  opacity: 1;
}

input:focus + .slider {
  box-shadow: 0 0 3px #4285F4;
}

input:checked + .slider:before {
  transform: translateX(28px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.slider.round {
  border-radius: 30px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Status */
.status-container {
  display: flex;
  align-items: center;
  margin: 24px 28px 32px;
  padding: 20px 24px;
  background-color: var(--status-container-bg);
  border-radius: 16px;
  box-shadow: 0 4px 16px var(--shadow-small);
  border: 1px solid var(--border-color);
  border-left: 5px solid var(--accent-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 60px;
}

.status-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%);
  z-index: 0;
}

.status-container > * {
  position: relative;
  z-index: 1;
}

/* Status icons */
.status-container.active::after {
  content: "✓";
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--accent-success);
  font-weight: bold;
}

.status-container.inactive::after {
  content: "○";
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--accent-danger);
  font-weight: bold;
}

.status-container.processing::after {
  content: "⟳";
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: var(--accent-warning);
  font-weight: bold;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Bot detection status styling */
.status-container.bot-detected {
  border-left: 4px solid #FF0000;
  background-color: rgba(255, 0, 0, 0.1);
  position: relative;
  padding-right: 40px;
}

.status-container.bot-detected::after {
  content: "⚠️";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  animation: pulse-warning 2s infinite;
}

[data-theme="dark"] .status-container.bot-detected {
  background-color: rgba(255, 0, 0, 0.2);
}

@keyframes pulse-warning {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
}



/* Loading state styling */
.status-container.loading {
  border-left: 4px solid #FBBC05;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-label {
  font-weight: 700;
  margin-right: 16px;
  color: var(--accent-primary);
  transition: color 0.3s ease;
  font-size: 15px;
}

.status-value {
  color: var(--text-color);
  flex: 1;
  font-weight: 600;
  transition: color 0.3s ease;
  font-size: 15px;
}



/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: var(--text-secondary);
  padding: 16px 28px;
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.version {
  font-weight: 500;
}

.author {
  font-weight: 500;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.author-name {
  font-weight: 700;
  color: #4285F4;
  font-size: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  padding: 0 2px;
  text-shadow: 1px 1px 1px var(--shadow-small);
  transition: all 0.3s ease;
}

[data-theme="dark"] .author-name {
  color: #5C9CFF; /* Brighter blue for dark mode */
}

.author-name:hover {
  color: #EA4335;
  transform: scale(1.05);
}

/* Performance Container Styles */
.performance-container {
  margin: 12px 0;
  padding: 12px;
  background: var(--performance-bg);
  border: 1px solid var(--performance-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.performance-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.debug-toggle {
  background: var(--debug-btn-bg);
  border: none;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-toggle:hover {
  background: var(--debug-btn-active);
  transform: scale(1.05);
}

.debug-toggle.active {
  background: var(--debug-btn-active);
  box-shadow: 0 0 8px rgba(66, 133, 244, 0.3);
}

.debug-icon {
  width: 14px;
  height: 14px;
  fill: var(--text-color);
  transition: fill 0.2s ease;
}

.debug-toggle:hover .debug-icon,
.debug-toggle.active .debug-icon {
  fill: white;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.metric {
  background: var(--metric-bg);
  border-radius: 6px;
  padding: 8px 6px;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.metric:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--shadow-small);
}

.metric-label {
  font-size: 10px;
  color: var(--text-secondary);
  margin-bottom: 2px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color);
  font-family: 'Courier New', monospace;
}

.metric-value:not(:empty):not([data-value="-"]) {
  color: #4285F4;
}

/* Animation for performance container */
.performance-container.show {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Auto Handle Container */
